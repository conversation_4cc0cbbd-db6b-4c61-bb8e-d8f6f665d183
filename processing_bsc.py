from clients.morails_client import MoralisClient
from clients.coingecko_client import CoinGeckoClient
from datetime import datetime
import logging
from token_constants_bsc import BNB_NATIVE, BNB_TOKENS
import pandas as pd

def munge_transactions_bsc(swaps, moralis_client, coingecko_client):
    """
    Extract token information from BSC SWAP transactions from Moralis API.
    
    Args:
        swaps (list): List of swap objects from Moralis API
        moralis_client (MoralisClient): Moralis client instance
        coingecko_client (CoinGeckoClient): CoinGecko client instance
        
    Returns:
        tuple: (token_swaps list, token_id_mapping dict)
    """
    token_swaps = []
    
    for swap in swaps:
        try:
            # Extract basic swap information
            transaction_hash = swap.get("transactionHash")
            block_timestamp = swap.get("blockTimestamp")
            transaction_type = swap.get("transactionType")  # "buy" or "sell"
            
            # Parse timestamp
            if block_timestamp:
                # Convert ISO timestamp to Unix timestamp
                timestamp = int(datetime.fromisoformat(block_timestamp.replace('Z', '+00:00')).timestamp())
            else:
                logging.warning(f"No timestamp found for swap {transaction_hash}")
                continue
            
            # Get bought and sold token information
            bought_token = swap.get("bought", {})
            sold_token = swap.get("sold", {})
            
            if not bought_token or not sold_token:
                logging.warning(f"Missing token information for swap {transaction_hash}")
                continue
            
            # Extract token addresses and amounts
            bought_address = bought_token.get("address", "").lower()
            sold_address = sold_token.get("address", "").lower()
            
            # Handle amounts (they come as strings with decimals already applied)
            bought_amount = float(bought_token.get("amount", 0))
            sold_amount = abs(float(sold_token.get("amount", 0)))  # Make positive
            
            # Get USD prices
            bought_usd_price = bought_token.get("usdPrice", 0)
            sold_usd_price = sold_token.get("usdPrice", 0)
            
            # Determine input/output based on transaction type
            if transaction_type == "buy":
                # When buying, we sell something (input) to buy the target token (output)
                input_address = sold_address
                output_address = bought_address
                input_amount = sold_amount
                output_amount = bought_amount
                input_usd_price = sold_usd_price
                output_usd_price = bought_usd_price
                action = "buy"
                # Price is how much we paid per unit of the bought token
                price = input_amount / output_amount if output_amount > 0 else 0
            else:  # "sell"
                # When selling, we sell the target token (input) to get something (output)
                input_address = bought_address  # What we received
                output_address = sold_address   # What we sold
                input_amount = bought_amount
                output_amount = sold_amount
                input_usd_price = bought_usd_price
                output_usd_price = sold_usd_price
                action = "sell"
                # Price is how much we received per unit of the sold token
                price = input_amount / output_amount if output_amount > 0 else 0
            
            # Check if this involves BNB (native or wrapped)
            is_bnb_trade = (
                input_address in [addr.lower() for addr in BNB_TOKENS] or
                output_address in [addr.lower() for addr in BNB_TOKENS] or
                bought_token.get("symbol") == "BNB" or
                sold_token.get("symbol") == "BNB"
            )
            
            # Adjust price calculation for BNB trades
            if is_bnb_trade:
                # Get BNB price for this date
                bnb_price = moralis_client.get_bnb_price()  # Current price, could be improved with historical
                if bnb_price and bnb_price > 0:
                    # If input is BNB, multiply price by BNB price to get USD value
                    if (input_address in [addr.lower() for addr in BNB_TOKENS] or 
                        sold_token.get("symbol") == "BNB"):
                        price = price * bnb_price
                    # If output is BNB, multiply price by BNB price to get USD value  
                    elif (output_address in [addr.lower() for addr in BNB_TOKENS] or
                          bought_token.get("symbol") == "BNB"):
                        price = price * bnb_price
            
            # Use USD prices if available and more reliable
            if input_usd_price > 0 and output_usd_price > 0:
                if action == "buy":
                    price = input_usd_price  # Price paid per unit
                else:
                    price = output_usd_price  # Price received per unit
            
            # Create swap entry
            token_swaps.append({
                "timestamp": timestamp,
                "date": datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                "signature": transaction_hash,
                "user_account": swap.get("walletAddress"),
                "input_mint": input_address,
                "output_mint": output_address,
                "input_id": "",  # Will be filled later
                "output_id": "",  # Will be filled later
                "input_amount": input_amount,
                "output_amount": output_amount,
                "action": action,
                "price": price,
                "is_bnb_trade": is_bnb_trade,
                "bought_symbol": bought_token.get("symbol", "UNKNOWN"),
                "sold_symbol": sold_token.get("symbol", "UNKNOWN")
            })
            
        except Exception as e:
            logging.error(f"Error processing swap {swap.get('transactionHash')}: {str(e)}")
    
    # Extract unique token addresses
    unique_addresses = set()
    for swap in token_swaps:
        unique_addresses.add(swap["input_mint"])
        unique_addresses.add(swap["output_mint"])
    
    # Remove empty addresses
    unique_addresses.discard("")
    unique_addresses.discard(None)
    
    # Create token ID mapping using CoinGecko client
    logging.info(f"Creating token ID mapping for {len(unique_addresses)} unique token addresses")
    token_id_mapping = coingecko_client.create_bsc_token_id_mapping(unique_addresses)
    
    # Update input_id and output_id fields with token IDs from mapping
    for swap in token_swaps:
        swap["input_id"] = token_id_mapping.get(swap["input_mint"])
        swap["output_id"] = token_id_mapping.get(swap["output_mint"])
    
    return token_swaps, token_id_mapping

def munge_swaps_bsc(token_swaps, excluded_tokens):
    """
    Process BSC swaps, extracting the amount not associated with excluded tokens.
    
    Args:
        token_swaps (list): List of dictionaries containing token swap data
        excluded_tokens (set): Set of token addresses to exclude when determining amount
        
    Returns:
        DataFrame: Processed swaps with amount reflecting non-excluded token
    """
    munged_swaps = []
    
    # Convert excluded_tokens to lowercase for comparison
    excluded_tokens_lower = {token.lower() for token in excluded_tokens}
    
    for swap in token_swaps:
        amount = None
        
        # Determine the amount based on non-excluded tokens
        input_mint_lower = swap["input_mint"].lower()
        output_mint_lower = swap["output_mint"].lower()
        
        if input_mint_lower not in excluded_tokens_lower:
            amount = swap["input_amount"]
        elif output_mint_lower not in excluded_tokens_lower:
            amount = swap["output_amount"]
        else:
            logging.warning(f"Both input and output tokens are excluded for swap {swap['signature']}")
            continue  # Skip this swap if both tokens are excluded
            
        # Create a new dictionary with the required fields
        munged_swap = {
            "date": swap["date"],
            "input_mint": swap["input_mint"],
            "output_mint": swap["output_mint"],
            "input_id": swap["input_id"],
            "output_id": swap["output_id"],
            "amount": amount,
            "action": swap["action"],
            "price": swap["price"],
            "is_bnb_trade": swap["is_bnb_trade"]
        }
        munged_swaps.append(munged_swap)
        
    # Convert to DataFrame
    df = pd.DataFrame(munged_swaps)
    
    # Sort by date
    if not df.empty:
        df = df.sort_values("date")
        
    return df
