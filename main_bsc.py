import os
import argparse
import logging
from dotenv import load_dotenv
from clients.morails_client import MoralisClient
from clients.coingecko_client import CoinGeckoClient
from processing_bsc import munge_transactions_bsc, munge_swaps_bsc
from analysis_bsc import analyze_tokens_from_swaps_bsc
from utils.utils import ensure_output_dir, save_token_metadata
from token_constants_bsc import EXCLUDED_TOKENS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("bsc_token_analyzer.log"),
        logging.StreamHandler(),
    ],
)


def setup_args():
    """Set up command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze BSC token SWAP transactions"
    )
    parser.add_argument(
        "--wallet", type=str, help="BSC wallet address to analyze (overrides .env)"
    )
    parser.add_argument(
        "--months", type=int, default=6, help="Number of months to look back"
    )
    parser.add_argument(
        "--output", type=str, default="output_bsc", help="Output directory for results"
    )
    parser.add_argument(
        "--moralis-key", type=str, help="Moralis API key (overrides .env)"
    )
    parser.add_argument(
        "--coingecko-key", type=str, help="CoinGecko API key (overrides .env)"
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    return parser.parse_args()


def main():
    """Main function to analyze BSC token transactions."""
    # Load environment variables
    load_dotenv()

    # Parse command line arguments
    args = setup_args()

    # Set logging level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Get wallet address from args or .env
    wallet_address = args.wallet or os.getenv("WALLET_ADDRESS")
    if not wallet_address:
        logging.error(
            "Wallet address is required. Set it in .env file or provide with --wallet."
        )
        return

    # Validate BSC address format
    if not wallet_address.startswith("0x") or len(wallet_address) != 42:
        logging.error(
            f"Invalid BSC wallet address format: {wallet_address}. "
            "BSC addresses must start with '0x' and be 42 characters long."
        )
        return

    # Get API keys from args or .env
    moralis_api_key = args.moralis_key or os.getenv("MORALIS_API_KEY")
    coingecko_api_key = args.coingecko_key or os.getenv("COINGECKO_API_KEY")

    if not moralis_api_key:
        logging.error(
            "Moralis API key is required. Set it in .env file or provide with --moralis-key."
        )
        return

    if not coingecko_api_key:
        logging.error(
            "CoinGecko API key is required. Set it in .env file or provide with --coingecko-key."
        )
        return

    # Ensure output and cache directories exist
    ensure_output_dir(args.output)

    # Initialize clients
    moralis_client = MoralisClient(moralis_api_key)
    coingecko_client = CoinGeckoClient(coingecko_api_key)

    # Step 1: Fetch SWAP transactions
    logging.info(f"Analyzing BSC token SWAP transactions for wallet: {wallet_address}")
    logging.info(f"Looking back {args.months} months")

    swaps = moralis_client.get_swaps(wallet_address, months=args.months)

    if not swaps:
        logging.warning(f"No SWAP transactions found for wallet {wallet_address}")
        return

    logging.info(f"Found {len(swaps)} SWAP transactions")

    # Step 2: Extract token information from SWAP transactions
    token_swaps, token_id_mapping = munge_transactions_bsc(
        swaps, moralis_client, coingecko_client
    )

    if not token_swaps:
        logging.warning("No token swaps extracted from transactions")
        return

    logging.info(f"Extracted {len(token_swaps)} token swap events")

    # Get ATH data for each token using the CoinGeckoClient
    token_ath_data = coingecko_client.get_tokens_ath(token_id_mapping)

    # Get genesis dates for each token
    token_genesis_data = coingecko_client.get_tokens_genesis_dates(token_id_mapping)

    munged_swaps_df = munge_swaps_bsc(token_swaps, EXCLUDED_TOKENS)

    # Analyze tokens using the BSC-specific function
    analyzed_tokens = analyze_tokens_from_swaps_bsc(
        token_id_mapping, munged_swaps_df, token_ath_data, coingecko_client, token_genesis_data
    )

    # Save token metadata
    for token_metadata in analyzed_tokens:
        save_token_metadata(token_metadata, args.output)

    logging.info(f"BSC analysis complete. Results saved to {args.output} directory")


if __name__ == "__main__":
    main()
